name: Publish Development Build
on: push

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
            persist-credentials: false

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21

      - name: Build with Gradle
        run: ./gradlew build

      - name: Release
        uses: marvinpinto/action-automatic-releases@latest
        with:
          repo_token: '${{ secrets.GITHUB_TOKEN }}'
          automatic_release_tag: snapshot
          prerelease: true
          title: Dev Build
          files: |
            ./build/libs/*.jar
