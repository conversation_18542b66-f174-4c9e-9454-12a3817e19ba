# Meteor Addon Miku

English | [中文](README.md)

A [Meteor Client](https://meteorclient.com/) addon that extends functionality with practical automation and enhancement modules.

![Minecraft](https://img.shields.io/badge/Minecraft-1.21.x%20~%201.21-green.svg?style=for-the-badge&logo=minecraft)
![Language](https://img.shields.io/badge/Language-Chinese-blue.svg?style=for-the-badge)

## Features

### **Auto Trash**
Automatically discard specified items from your inventory with customizable groups and delays.

### **Auto Sand Miner**
Intelligent sand mining with Baritone integration, inventory management, and tool maintenance.

### **Auto Use Items**
Automatically use items based on timers or conditions (health, hunger) with priority settings.

### **Seed Mine**
Advanced ore ESP using world seed calculations. Precisely shows ore locations even on anti-xray servers.
- Supports all dimensions (Overworld, Nether, End)
- Baritone integration for automated mining
- Biome-specific ore rendering

## Installation

1. Download the latest `.jar` from [Releases](https://github.com/mikumiku7/meteor-miku/releases)
2. Place it in your `mods` folder
3. Ensure Meteor Client and Fabric Loader are installed
4. Launch the game

## Usage

1. Press `Right Shift` to open Meteor Client GUI
2. Find the addon modules in the categories
3. Configure settings as needed

## Language Support

This addon currently **only supports Chinese**. For proper text display, install [meteor_chinese](https://github.com/xingke0/meteor_chinese).

## License

This project is licensed under [GPL3](LICENSE).

Based on [meteor-addon-template](https://github.com/MeteorDevelopment/meteor-addon-template).
