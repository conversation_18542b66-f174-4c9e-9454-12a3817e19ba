plugins {
    id("fabric-loom") version "1.9-SNAPSHOT"
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

val archivesBaseName: String by project
val modVersion: String by project
val mavenGroup: String by project

base {
    archivesName.set(archivesBaseName)
}

version = modVersion
group = mavenGroup

repositories {
    mavenCentral()
    mavenLocal()
    maven {
        name = "Meteor Dev Releases"
        url = uri("https://maven.meteordev.org/releases")
    }
    maven {
        name = "Meteor Dev Snapshots"
        url = uri("https://maven.meteordev.org/snapshots")
    }
    maven {
        name = "SeedFinding"
        url = uri("https://maven.seedfinding.com/")
    }
    maven {
        name = "SeedFinding Snapshots"
        url = uri("https://maven-snapshots.seedfinding.com/")
    }
    maven {
        name = "JitPack"
        url = uri("https://jitpack.io")
    }
    maven {
        name = "Du<PERSON> Ma<PERSON>"
        url = uri("https://maven.duti.dev/releases")
    }
}

loom {
    accessWidenerPath.set(file("src/main/resources/meteor-rejects.accesswidener"))
}

// Configuration that holds jars to include in the jar
val extraLibs: Configuration by configurations.creating

dependencies {
    val minecraftVersion: String by project
    val yarnVersion: String by project
    val loaderVersion: String by project
    val baritoneVersion: String by project

    // This will make it work on most platforms. It automatically chooses the right dependencies at runtime.
    extraLibs("dev.duti.acheong:cubiomes:1.22.5") { isTransitive = false }
    extraLibs("dev.duti.acheong:cubiomes:1.22.5:linux64") { isTransitive = false }
    extraLibs("dev.duti.acheong:cubiomes:1.22.5:osx") { isTransitive = false }
    extraLibs("dev.duti.acheong:cubiomes:1.22.5:windows64") { isTransitive = false }

    // To change the versions see the gradle.properties file
    minecraft("com.mojang:minecraft:$minecraftVersion")
    mappings("net.fabricmc:yarn:$yarnVersion:v2")
    modImplementation("net.fabricmc:fabric-loader:$loaderVersion")

    modImplementation("meteordevelopment:meteor-client:$minecraftVersion-SNAPSHOT")
    modCompileOnly("meteordevelopment:baritone:$baritoneVersion-SNAPSHOT")

    // seed .locate and ore sim
    extraLibs("com.seedfinding:mc_math:ffd2edcfcc0d18147549c88cc7d8ec6cf21b5b91") { isTransitive = false }
    extraLibs("com.seedfinding:mc_seed:1ead6fcefe7e8de4b3d60cd6c4e993f1e8f33409") { isTransitive = false }
    extraLibs("com.seedfinding:mc_core:1.210.0") { isTransitive = false }
    extraLibs("com.seedfinding:mc_noise:7e3ba65e181796c4a2a1c8881d840b2254b92962") { isTransitive = false }
    extraLibs("com.seedfinding:mc_biome:41a42cb9019a552598f12089059538853e18ec78") { isTransitive = false }
    extraLibs("com.seedfinding:mc_terrain:b4246cbd5880c4f8745ccb90e1b102bde3448126") { isTransitive = false }
    extraLibs("com.seedfinding:mc_feature:919b7e513cc1e87e029a9cd703fc4e2dc8686229") { isTransitive = false }

    // seedcracker api
    implementation(include("com.github.19MisterX98.SeedcrackerX:seedcrackerx-api:2.10.1")!!) { isTransitive = false }
    // implementation(include("com.github.19MisterX98.SeedcrackerX:seedcrackerx-api:master-SNAPSHOT")!!) { isTransitive = false }

    configurations.implementation.get().extendsFrom(extraLibs)
}

tasks.processResources {
    inputs.property("version", project.version)

    filesMatching("fabric.mod.json") {
        expand(
            mapOf(
                "version" to project.version,
                "mc_version" to project.property("minecraft_version"),
                "gh_hash" to (System.getenv("GITHUB_SHA") ?: "")
            )
        )
    }
}

tasks.jar {
    from("LICENSE") {
        rename { "${it}_${project.base.archivesName.get()}" }
    }
    from({
        extraLibs.map { if (it.isDirectory) it else zipTree(it) }
    })
}

tasks.withType<Jar> {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.withType<JavaCompile>().configureEach {
    // ensure that the encoding is set to UTF-8, no matter what the system default is
    // this fixes some edge cases with special characters not displaying correctly
    // see http://yodaconditions.net/blog/fix-for-java-file-encoding-problems-with-gradle.html
    // If Javadoc is generated, this must be specified in that task too.
    options.encoding = "UTF-8"

    // The Minecraft launcher currently installs Java 8 for users, so your mod probably wants to target Java 8 too
    // JDK 9 introduced a new way of specifying this that will make sure no newer classes or methods are used.
    // We'll use that if it's available, but otherwise we'll use the older option.
    val targetVersion = 21
    if (JavaVersion.current().isJava9Compatible) {
        options.release.set(targetVersion)
    }
}
