name: checks
on:
  - pull_request
  - push
jobs:
  build:
    strategy:
      matrix:
        os:
          - macos-latest
          - windows-latest
          - ubuntu-latest
    runs-on: '${{ matrix.os }}'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
            persist-credentials: false

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21

      - name: Build with Gradle
        run: ./gradlew build
