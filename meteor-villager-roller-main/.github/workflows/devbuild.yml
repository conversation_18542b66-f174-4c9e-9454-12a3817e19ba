name: devbuild
on:
  - push
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Wrapper Validation
        uses: gradle/actions/wrapper-validation@v3
      - name: Setup JDK
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: adopt
      - name: Build
        run: |
          chmod +x ./gradlew
          ./gradlew build
      - name: Create Dev Build release
        uses: marvinpinto/action-automatic-releases@latest
        with:
          repo_token: '${{ secrets.GITHUB_TOKEN }}'
          automatic_release_tag: latest
          prerelease: true
          title: Dev Build
          files: |
            ./build/libs/*.jar
