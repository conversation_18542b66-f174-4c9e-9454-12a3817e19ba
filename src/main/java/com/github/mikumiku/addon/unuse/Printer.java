//package com.github.mikumiku.addon;
//
/// *
// * Decompiled with CFR 0.152.
// *
// * Could not load the following classes:
// *  fi.dy.masa.litematica.data.DataManager
// *  fi.dy.masa.litematica.world.SchematicWorldHandler
// *  fi.dy.masa.litematica.world.WorldSchematic
// *  net.minecraft.class_1802
// *  net.minecraft.class_2246
// *  net.minecraft.class_2248
// *  net.minecraft.class_2338
// *  net.minecraft.class_2350
// *  net.minecraft.class_2510
// *  net.minecraft.class_2561
// *  net.minecraft.class_2680
// *  net.minecraft.class_2741
// *  net.minecraft.class_2769
// */
//
//import de.florianmichael.waybackauthlib.WaybackAuthLib$AuthenticateRefreshResponse$ConstantPool;
//import fi.dy.masa.litematica.data.DataManager;
//import fi.dy.masa.litematica.world.SchematicWorldHandler;
//import fi.dy.masa.litematica.world.WorldSchematic;
//import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthRequestDecoder$State$ConstantPool;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import javassist.ClassPoolTail$ConstantPool;
//import javassist.bytecode.CodeIterator$Lookup$ConstantPool;
//import javassist.bytecode.StackMap$Writer$ConstantPool;
//import javassist.bytecode.StringInfo$ConstantPool;
//import javassist.bytecode.stackmap.TypeTag$ConstantPool;
//import javassist.tools.reflect.Metaobject$ConstantPool;
//import javassist.util.proxy.SecurityActions$TheUnsafe$ConstantPool;
//import meteordevelopment.discordipc.connection.UnixConnection$ConstantPool;
//import meteordevelopment.meteorclient.commands.commands.FakePlayerCommand$ConstantPool;
//import meteordevelopment.meteorclient.events.entity.player.BlockBreakingCooldownEvent$ConstantPool;
//import meteordevelopment.meteorclient.events.render.Render3DEvent;
//import meteordevelopment.meteorclient.events.world.TickEvent;
//import meteordevelopment.meteorclient.gui.renderer.packer.TexturePacker$Image$ConstantPool;
//import meteordevelopment.meteorclient.gui.screens.settings.PacketBoolSettingScreen$ConstantPool;
//import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorWindow$ConstantPool;
//import meteordevelopment.meteorclient.renderer.PostProcessRenderer$ConstantPool;
//import meteordevelopment.meteorclient.renderer.ShapeMode;
//import meteordevelopment.meteorclient.renderer.text.FontInfo$ConstantPool;
//import meteordevelopment.meteorclient.settings.BoolSetting;
//import meteordevelopment.meteorclient.settings.ColorSetting;
//import meteordevelopment.meteorclient.settings.EnumSetting;
//import meteordevelopment.meteorclient.settings.IntSetting;
//import meteordevelopment.meteorclient.settings.Setting;
//import meteordevelopment.meteorclient.settings.SettingGroup;
//import meteordevelopment.meteorclient.systems.hud.screens.HudEditorScreen$1$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.Categories;
//import meteordevelopment.meteorclient.systems.modules.Module;
//import meteordevelopment.meteorclient.systems.modules.combat.AutoTrap$TopMode$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.combat.Surround$BlockType$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.ggboy.SinglePearl$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.movement.GrimFastWeb$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$StaticGroundListener$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.player.AutoReplenish$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.render.BetterTab$1$ConstantPool;
//import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$Rotation$ConstantPool;
//import meteordevelopment.meteorclient.utils.network.JsonBodyHandler$ConstantPool;
//import meteordevelopment.meteorclient.utils.player.InvUtils;
//import meteordevelopment.meteorclient.utils.render.color.SettingColor;
//import meteordevelopment.meteorclient.utils.schematic.SchematicBlockState;
//import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
//import meteordevelopment.meteorclient.utils.world.BlockUtilGrim;
//import meteordevelopment.meteorclient.utils.world.BlockUtils;
//import meteordevelopment.orbit.EventHandler;
//import net.minecraft.class_1802;
//import net.minecraft.class_2246;
//import net.minecraft.class_2248;
//import net.minecraft.class_2338;
//import net.minecraft.class_2350;
//import net.minecraft.class_2510;
//import net.minecraft.class_2561;
//import net.minecraft.class_2680;
//import net.minecraft.class_2741;
//import net.minecraft.class_2769;
//import org.reflections.scanners.MethodAnnotationsScanner$ConstantPool;
//import org.reflections.vfs.JarInputFile$ConstantPool;
//
//public class Printer
//    extends Module {
//    private final SettingGroup sgGeneral;
//    private final SettingGroup renderGeneral;
//    private final Setting<Boolean> moveStop;
//    private final Setting<PlaceMode> placeMode;
//    private final Setting<Integer> placeNums;
//    private final Setting<Integer> placeDelay;
//    private final Setting<Integer> placeRange;
//    private final Setting<Boolean> render;
//    private final Setting<ShapeMode> shapeMode;
//    private final Setting<SettingColor> readySideColor;
//    private final Setting<SettingColor> readyLineColor;
//    private ArrayList<class_2338> placePos;
//    private Map<class_2338, class_2680> placePosHash;
//    private int delay;
//    List<class_2248> blocksNoOp;
//
//    public Printer() {
//        super(Categories.Ggboy, Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(FakePlayerCommand$ConstantPool.const_GSklVbCv3zVNSs1))), new StringBuilder(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(Socks5PasswordAuthRequestDecoder$State$ConstantPool.const_7aeXzoP5GzzTjd4)))), Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(WMeteorWindow$ConstantPool.const_xS0eEVv64TaTrNm))), new String[0]);
//        this.sgGeneral = this.settings.getDefaultGroup();
//        this.renderGeneral = this.settings.createGroup(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(CodeIterator$Lookup$ConstantPool.const_7uFcE1P3DTNldrr))));
//        this.moveStop = this.sgGeneral.add(((BoolSetting.Builder)((BoolSetting.Builder)((BoolSetting.Builder)new BoolSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(HudEditorScreen$1$ConstantPool.const_yff29uaNfGeVSXa))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(AutoReplenish$ConstantPool.const_8EvI6WaYgBegbVz))))).defaultValue(false)).build());
//        this.placeMode = this.sgGeneral.add(((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(BlockBreakingCooldownEvent$ConstantPool.const_ji6g0CZulYktr90))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(JsonBodyHandler$ConstantPool.const_SHNZl8IYfANbAAN))))).defaultValue(PlaceMode.STRICT)).build());
//        this.placeNums = this.sgGeneral.add(((IntSetting.Builder)((IntSetting.Builder)((IntSetting.Builder)new IntSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(PacketBoolSettingScreen$ConstantPool.const_0vBDNyFYD2ntSrz))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(HighwayBuilder$Rotation$ConstantPool.const_cB7RQGqFl9gxcwQ))))).defaultValue(1)).sliderRange(1, 6).build());
//        this.placeDelay = this.sgGeneral.add(((IntSetting.Builder)((IntSetting.Builder)((IntSetting.Builder)new IntSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(ElytraFly$StaticGroundListener$ConstantPool.const_LrTDL3jaPg7IkBl))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(ClassPoolTail$ConstantPool.const_aSjViwojvEtTblA))))).defaultValue(0)).sliderRange(0, 10).build());
//        this.placeRange = this.sgGeneral.add(((IntSetting.Builder)((IntSetting.Builder)((IntSetting.Builder)new IntSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(FontInfo$ConstantPool.const_4g8DMEzADOMLfTs))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(AutoTrap$TopMode$ConstantPool.const_9wv6jo9VXk8dqYs))))).defaultValue(4)).sliderRange(0, 6).build());
//        this.render = this.renderGeneral.add(((BoolSetting.Builder)((BoolSetting.Builder)((BoolSetting.Builder)new BoolSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(BetterTab$1$ConstantPool.const_HrDMJdG5TuwLbO1))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(JarInputFile$ConstantPool.const_h1VObhyp92mjGzN))))).defaultValue(true)).build());
//        this.shapeMode = this.renderGeneral.add(((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(Surround$BlockType$ConstantPool.const_YiYZJ2e1yD3wIWV))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(UnixConnection$ConstantPool.const_tVKIWUeRbgrPrDH))))).defaultValue(ShapeMode.Both)).build());
//        this.readySideColor = this.renderGeneral.add(((ColorSetting.Builder)((ColorSetting.Builder)new ColorSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(GrimFastWeb$ConstantPool.const_ECuApBR533Miyue))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(Metaobject$ConstantPool.const_SS8XMEma3IWjyso))))).defaultValue(new SettingColor(255, 255, 255, 50)).build());
//        this.readyLineColor = this.renderGeneral.add(((ColorSetting.Builder)((ColorSetting.Builder)new ColorSetting.Builder().name(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(TexturePacker$Image$ConstantPool.const_NFar91BN27ywyxP))))).description(Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(PostProcessRenderer$ConstantPool.const_S1wykAo7LglU4Je))))).defaultValue(new SettingColor(255, 255, 255, 255)).build());
//        this.placePos = new ArrayList();
//        this.placePosHash = new HashMap<class_2338, class_2680>();
//        this.delay = 0;
//        this.blocksNoOp = Arrays.asList(class_2246.field_10282);
//    }
//
//    @Override
//    public void onActivate() {
//        if (!CheckUtils.check()) {
//            for (int i = 0; i < 3; ++i) {
//                this.info(class_2561.method_30163((String)Printer.fsYD54f2G9(Printer.DSVV2GHvmQ(Printer.Wf9YOSdOGW(StackMap$Writer$ConstantPool.const_6zO4Twbg5S4dJDU)))));
//            }
//            if (this.isActive()) {
//                this.toggle();
//            }
//        }
//        this.delay = 0;
//    }
//
//    @Override
//    public void onDeactivate() {
//        this.delay = 0;
//    }
//
//    @Override
//    public void init() {
//        if (this.isActive()) {
//            this.toggle();
//        }
//    }
//
//    @EventHandler
//    public void onTick(TickEvent.Post event) {
//        if (this.moveStop.get().booleanValue() && (Printer.mc.field_1690.field_1881.method_1434() || Printer.mc.field_1690.field_1894.method_1434() || Printer.mc.field_1690.field_1913.method_1434() || Printer.mc.field_1690.field_1849.method_1434())) {
//            return;
//        }
//        WorldSchematic worldSchematic = SchematicWorldHandler.getSchematicWorld();
//        if (Printer.mc.field_1724 == null) {
//            return;
//        }
//        if (worldSchematic == null) {
//            return;
//        }
//        if (this.delay < this.placeDelay.get()) {
//            ++this.delay;
//            return;
//        }
//        this.delay = 0;
//        this.setPlacePos();
//        if (this.placePos.size() == 0) {
//            return;
//        }
//        int nums = Math.min(this.placeNums.get(), this.placePos.size());
//        for (int i = 0; i < nums; ++i) {
//            class_2338 pos = this.placePos.get(i);
//            class_2680 state = this.placePosHash.get(pos);
//            int slot = InvUtils.findItemInventorySlotGrim(state.method_26204().method_8389());
//            if (slot == -1) continue;
//            if (state.method_28501().contains(class_2741.field_12525)) {
//                direction = (class_2350)state.method_11654((class_2769)class_2741.field_12525);
//                if (direction == class_2350.field_11036 || direction == class_2350.field_11033) continue;
//                InvUtils.doSwap(slot);
//                BlockUtilGrim.placeBlockByFaceDirection(pos, true, true, true, this.shouuldNoOp(state.method_26204()) ? direction : direction.method_10153());
//            } else if (state.method_28501().contains(class_2741.field_12545)) {
//                direction = (class_2350)state.method_11654((class_2769)class_2741.field_12545);
//                if (direction == class_2350.field_11036 || direction == class_2350.field_11033) continue;
//                InvUtils.doSwap(slot);
//                BlockUtilGrim.placeBlockByFaceDirection(pos, true, true, true, direction.method_10153());
//            } else if (state.method_28501().contains(class_2741.field_12481)) {
//                direction = (class_2350)state.method_11654((class_2769)class_2741.field_12481);
//                if (direction == class_2350.field_11036 || direction == class_2350.field_11033) continue;
//                InvUtils.doSwap(slot);
//                BlockUtilGrim.placeBlockByFaceDirection(pos, true, true, true, this.shouuldNoOp(state.method_26204()) ? direction : direction.method_10153());
//            } else {
//                InvUtils.doSwap(slot);
//                BlockUtilGrim.placeBlock(pos, true, true, true);
//            }
//            InvUtils.doSwap(slot);
//            InvUtils.sync();
//        }
//    }
//
//    private boolean shouuldNoOp(class_2248 block) {
//        if (this.blocksNoOp.contains(block)) {
//            return true;
//        }
//        return block instanceof class_2510;
//    }
//
//    public ArrayList<class_2338> setPlacePos() {
//        WorldSchematic worldSchematic = SchematicWorldHandler.getSchematicWorld();
//        ArrayList<class_2338> sphere = BlockUtils.getSphere(this.placeRange.get().intValue());
//        this.placePos.clear();
//        this.placePosHash.clear();
//        for (class_2338 pos : sphere) {
//            SchematicBlockState state = new SchematicBlockState(Printer.mc.field_1724.method_37908(), worldSchematic, pos);
//            if (!(this.placeMode.get() == PlaceMode.LEGIT ? BlockUtils.canPlace_alien(pos, this.placeRange.get().intValue(), true) : BlockUtilGrim.getInteractDirection(pos, true) != null) || state.targetState.method_26204() == class_2246.field_10124 || state.targetState.method_26204() == state.currentState.method_26204() || pos.method_10264() > DataManager.getRenderLayerRange().getLayerMax() || state.targetState.method_26204().method_8389() == class_1802.field_8162 || !InvUtils.find(state.targetState.method_26204().method_8389()).found() || this.placePos.contains(pos)) continue;
//            this.placePos.add(pos);
//            this.placePosHash.put(pos, state.targetState);
//        }
//        return this.placePos;
//    }
//
//    @EventHandler
//    private void onRender(Render3DEvent event) {
//        if (this.render.get().booleanValue() && this.placePos.size() > 0) {
//            for (int i = 0; i < this.placePos.size(); ++i) {
//                double x1 = this.placePos.get(i).method_10263();
//                double y1 = this.placePos.get(i).method_10264();
//                double z1 = this.placePos.get(i).method_10260();
//                double x2 = this.placePos.get(i).method_10263() + 1;
//                double y2 = this.placePos.get(i).method_10264() + 1;
//                double z2 = this.placePos.get(i).method_10260() + 1;
//                event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
//            }
//        }
//    }
//
//    private static /* bridge */ /* synthetic */ String fsYD54f2G9(String string) {
//        StringBuilder stringBuilder = new StringBuilder();
//        int n = 0;
//        while (n < string.length()) {
//            char c = string.charAt(n);
//            int n2 = SinglePearl$ConstantPool.const_9IYbYowl2oajhgD;
//            int n3 = -c + -1;
//            stringBuilder.append((char)(n2 + ((-n2 + -1 | ~c) - ~c) - (n3 + (n2 & ~n3) - (-c + -1))));
//            ++n;
//        }
//        return stringBuilder.toString();
//    }
//
//    private static /* bridge */ /* synthetic */ String DSVV2GHvmQ(String string) {
//        StringBuilder stringBuilder = new StringBuilder();
//        int n = 0;
//        while (n < string.length()) {
//            char c = string.charAt(n);
//            int n2 = WaybackAuthLib$AuthenticateRefreshResponse$ConstantPool.const_IFXKs58ijnmIo5l;
//            int n3 = n2 + ((-n2 + -1 | ~c) - ~c);
//            int n4 = ~c + 1 + -1;
//            int n5 = ~(n2 - 1) + -1;
//            int n6 = -n3 + -1;
//            stringBuilder.append((char)(n6 + (n4 + ((-n4 + -1 | ~n5) - ~n5) & ~n6) - (-n3 + -1)));
//            ++n;
//        }
//        return stringBuilder.toString();
//    }
//
//    private static /* bridge */ /* synthetic */ String Wf9YOSdOGW(String string) {
//        StringBuilder stringBuilder = new StringBuilder();
//        int n = 0;
//        while (n < string.length()) {
//            char c = string.charAt(n);
//            int n2 = MethodAnnotationsScanner$ConstantPool.const_8DYORkU3NVSjlul;
//            int n3 = -c + -1;
//            int n4 = n3 + (~n2 + 1 + -1 & ~n3) - (-c + -1);
//            int n5 = -n2 + -1;
//            int n6 = n5 + (~(c - '\u0001') + -1 & ~n5) - (-n2 + -1);
//            stringBuilder.append((char)(n6 + ((-n6 + -1 | ~n4) - ~n4)));
//            ++n;
//        }
//        return stringBuilder.toString();
//    }
//
//    /*
//     * Illegal identifiers - consider using --renameillegalidents true
//     */
//    private static enum PlaceMode {
//        STRICT,
//        LEGIT;
//
//
//        private static /* bridge */ /* synthetic */ String JI81leqnrD(String string) {
//            StringBuilder stringBuilder = new StringBuilder();
//            int n = 0;
//            while (n < string.length()) {
//                char c = string.charAt(n);
//                int n2 = TypeTag$ConstantPool.const_nSgjmGDtam79rdf;
//                int n3 = -c + -1;
//                stringBuilder.append((char)(n2 + ((-n2 + -1 | ~c) - ~c) - (n3 + (n2 & ~n3) - (-c + -1))));
//                ++n;
//            }
//            return stringBuilder.toString();
//        }
//
//        private static /* bridge */ /* synthetic */ String 7WuNJQGiWq(String string) {
//            StringBuilder stringBuilder = new StringBuilder();
//            int n = 0;
//            while (n < string.length()) {
//                char c = string.charAt(n);
//                int n2 = SecurityActions$TheUnsafe$ConstantPool.const_pV6zfjfNpPpBT4v;
//                int n3 = -c + -1;
//                stringBuilder.append((char)(n2 + ((-n2 + -1 | ~c) - ~c) - (n3 + (n2 & ~n3) - (-c + -1))));
//                ++n;
//            }
//            return stringBuilder.toString();
//        }
//
//        private static /* bridge */ /* synthetic */ String SeeNjASZIO(String string) {
//            StringBuilder stringBuilder = new StringBuilder();
//            int n = 0;
//            while (n < string.length()) {
//                char c = string.charAt(n);
//                int n2 = StringInfo$ConstantPool.const_AJBIyILtoFx4ayF;
//                int n3 = n2 + ((-n2 + -1 | ~c) - ~c);
//                int n4 = ~c + 1 + -1;
//                int n5 = ~(n2 - 1) + -1;
//                int n6 = -n3 + -1;
//                stringBuilder.append((char)(n6 + (n4 + ((-n4 + -1 | ~n5) - ~n5) & ~n6) - (-n3 + -1)));
//                ++n;
//            }
//            return stringBuilder.toString();
//        }
//    }
//}
//
